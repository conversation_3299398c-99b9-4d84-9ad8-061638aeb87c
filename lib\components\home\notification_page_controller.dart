// ignore_for_file: unused_field

import 'dart:developer';

import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/response/inbox_response.dart';
import 'package:pdl_superapp/models/tutor_model.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:showcaseview/showcaseview.dart';

class NotificationPageController extends BaseControllers {
  RxList<InboxModel> arrData = <InboxModel>[].obs;

  RxBool isReadAll = false.obs;

  // pagination
  int page = 0;
  int maxPage = 0;
  int limit = 10;
  bool enableLoadMore = true;
  bool? nextPage;
  bool? prevPage;
  ScrollController scrollController = ScrollController();

  // declare global key for tutorial use
  final GlobalKey _keyTutorNotif = GlobalKey();
  final GlobalKey _keyTutorInbox = GlobalKey();
  final GlobalKey _keyTutorMarkAllAsRead = GlobalKey();

  List<TutorModel> tutorData = [];

  @override
  void onInit() {
    super.onInit();
    _assignTutor();
    _getArguments();
    setupScrollListener();
    getInbox(isRefresh: true);
  }

  void _assignTutor() {
    tutorData = [
      TutorModel(
        number: 1,
        key: _keyTutorNotif,
        title: 'notification_str'.tr,
        description: 'tutor_desc_notif_str'.tr,
      ),
      // TutorModel(
      //   number: 2,
      //   key: _keyTutorInbox,
      //   title: 'mark_all_as_read_str'.tr,
      //   description: 'tutor_desc_mark_all_as_read_str'.tr,
      // ),
      TutorModel(
        number: 2,
        key: _keyTutorMarkAllAsRead,
        title: 'mark_all_as_read_str'.tr,
        description: 'tutor_desc_mark_all_as_read_str'.tr,
      ),
    ];
  }

  void setupScrollListener() {
    scrollController.addListener(() {
      if (scrollController.position.atEdge) {
        if (scrollController.position.pixels == 0) {
          // You're at the top.
        } else {
          // You're at the bottom.
          loadMore();
          log('loadMore');
        }
      }
    });
  }

  Future<void> loadMore() async {
    if (!enableLoadMore) {
      isLoading.value = false;
      setLoading(false);
      return;
    }
    if (page != (maxPage)) {
      getInbox();
    }
  }

  Future<void> getInbox({bool isRefresh = false}) async {
    setLoading(true);
    if (isRefresh) {
      page = 0;
      arrData.clear();
    }
    Map<String, dynamic> queryParams = {
      'page': page,
      'size': limit,
      // 'inboxType': 'NOTIFICATION',
      'isArchived': false,
      'deleted': false,
    };

    // queryParams['trxType'] =
    //     'RECRUITMENT_BP&trxType=RECRUITMENT_BM&trxType=RECRUITMENT_BD';

    await api.getInbox(
      controllers: this,
      code: kReqGetInbox,
      query: queryParams,
    );
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    switch (requestCode) {
      case kReqGetInbox:
        parsePagination(response);
        parseData(response);
        break;
      case kReqDeleteInbox:
        // getInbox(isRefresh: true);
        break;
      case kReqReadInbox:
        getInbox(isRefresh: true);
        break;
      default:
    }
  }

  parsePagination(response) {
    maxPage = response['totalPages'] ?? 0;
    bool isLastPage = response['last'] ?? true;

    if (!isLastPage) {
      page = page + 1;
      enableLoadMore = true;
    } else {
      enableLoadMore = false;
    }
  }

  parseData(response) {
    for (int i = 0; i < response['content'].length; i++) {
      InboxModel data = InboxModel.fromJson(response['content'][i]);
      arrData.add(data);
    }

    for (int i = 0; i < arrData.length; i++) {
      if (arrData[i].isRead == false) {
        log(arrData[i].isRead.toString());
        isReadAll.value = true;
        break;
      }
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
  }

  deleteNotification(int id) {
    api.deleteInboxSingle(controllers: this, id: id, code: kReqDeleteInbox);
  }

  readAll() async {
    setLoading(true);
    await api.postReadInbox(
      controllers: this,
      code: kReqReadInbox,
      ids: arrData.map((e) => e.id).toList(),
    );
  }

  void _getArguments() {
    if (Get.arguments != null) {
      if (Get.arguments['start_tutorial'] == true) {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          _startTutorial();
        });
      }
    }
  }

  void _startTutorial() {
    if (showCaseContext != null) {
      final keys = tutorData.map((e) => e.key).toList();
      ShowCaseWidget.of(showCaseContext!).startShowCase(keys);
    }
  }
}
