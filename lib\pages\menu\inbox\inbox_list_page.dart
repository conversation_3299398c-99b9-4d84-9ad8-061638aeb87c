import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_workers/utils/debouncer.dart';
import 'package:pdl_superapp/components/pdl_base_dialog.dart';
import 'package:pdl_superapp/components/pdl_dialog_content.dart';
import 'package:pdl_superapp/components/state/empty_state_view.dart';
import 'package:pdl_superapp/controllers/inbox/inbox_list_controller.dart';
import 'package:pdl_superapp/models/menu_inbox_models.dart';
import 'package:pdl_superapp/pages/menu/inbox/inbox_widgets/drawer_inbox.dart';
import 'package:pdl_superapp/pages/menu/inbox/inbox_widgets/item_inbox.dart';
import 'package:pdl_superapp/pages/menu/inbox/inbox_widgets/shimmer_list_inbox.dart';

import '../../../components/pdl_text_field.dart';
import '../../../utils/constants.dart';
import '../../../utils/utils.dart';

class InboxListPage extends StatefulWidget {
  const InboxListPage({super.key});

  @override
  State<InboxListPage> createState() => _InboxListPageState();
}

class _InboxListPageState extends State<InboxListPage> {
  final GlobalKey<ScaffoldState> _key = GlobalKey();

  TextTheme get textTheme => Theme.of(context).textTheme;

  final controller = Get.put(InboxListController());
  late ScrollController scrollController;
  final debouncer = Debouncer(delay: const Duration(milliseconds: 700));

  void _onSearchChanged(String query) {
    debouncer(() {
      FocusScope.of(context).unfocus();
      controller.q.value = query;
      controller.getInbox(isRefresh: true);
    });
  }

  void _loadMore() {
    if (scrollController.position.pixels >=
            (scrollController.position.maxScrollExtent - 100) &&
        !controller.isLoading.value) {
      if (controller.currentPage.value < controller.totalPages.value) {
        controller.currentPage.value += 1;
        controller.getInbox();
      }
    }
  }

  @override
  void initState() {
    scrollController = ScrollController()..addListener(_loadMore);
    super.initState();
    // ignore: use_build_context_synchronously
    Future.delayed(Duration.zero, () => FocusScope.of(context).unfocus());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      key: _key,
      drawer: Drawer(
        width: double.infinity,
        // This is the sidebar
        child: DrawerInbox(
          onChoose: (MenuInboxModel e) {
            if (e.inboxSelected == false) {
              for (var element in controller.menuInbox) {
                element.inboxSelected = false;
              }
              e.inboxSelected = true;
              controller.setSelectedMenu(e);
            }
            Navigator.pop(context);
          },
        ),
      ),
      // No built-in AppBar
      body: Stack(
        children: [
          Utils.cachedImageWrapper(
            'image/img-login-header.png',
            alignment: Alignment.topCenter,
          ),
          Container(
            width: Get.width,
            padding: EdgeInsets.symmetric(horizontal: paddingMedium),
            decoration: BoxDecoration(
              color: Get.isDarkMode ? kColorBgDark : kColorBgLight,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(15),
                topRight: Radius.circular(15),
              ),
            ),
            margin: EdgeInsets.only(top: 40),
            child: Column(
              children: [
                // Custom AppBar
                _AppBar(),
                Row(
                  children: [
                    Expanded(
                      child: PdlTextField(
                        hint: 'search_in_inbox_str'.tr,
                        onChanged: _onSearchChanged,
                        prefixIcon: Padding(
                          padding: EdgeInsets.symmetric(
                            vertical: paddingMedium,
                          ),
                          child: InkWell(
                            onTap: () => _key.currentState?.openDrawer(),
                            child: Utils.cachedSvgWrapper(
                              'icon/ic-linear-hamburger-menu.svg',
                              color:
                                  Get.isDarkMode
                                      ? kColorTextDark
                                      : kColorTextLight,
                            ),
                          ),
                        ),
                        suffixIcon: Padding(
                          padding: EdgeInsets.symmetric(
                            vertical: paddingMedium,
                          ),
                          child: Utils.cachedSvgWrapper(
                            'icon/ic-linear-search -2.svg',
                            color:
                                Get.isDarkMode
                                    ? kColorTextDark
                                    : kColorTextLight,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: paddingMedium),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Obx(
                    () => Text(
                      controller.selectedMenu.value?.title ?? "",
                      style: textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                Obx(() {
                  if ((controller.selectedMenu.value?.title ?? '')
                          .toLowerCase() ==
                      'sampah') {
                    return Container(
                      margin: EdgeInsets.only(top: paddingSmall),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: kColorGlobalBgWarning,
                      ),
                      padding: EdgeInsets.all(12),
                      child: Text(
                        'inbox_trash_delete_30days_str'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: kColorGlobalWarning,
                        ),
                      ),
                    );
                  }

                  return SizedBox.shrink();
                }),
                Expanded(
                  child: Obx(() {
                    if (controller.isLoading.value &&
                        controller.currentPage.value == 0) {
                      return ShimmerListInbox();
                    }

                    if (controller.inboxes.isEmpty) {
                      return EmptyStateView(msg: 'no_inbox_available_str'.tr);
                    }
                    return Column(
                      children: [
                        Expanded(
                          child: RefreshIndicator(
                            onRefresh: () async {
                              controller.getInbox(isRefresh: true);
                              return;
                            },
                            child: ListView.builder(
                              controller: scrollController,
                              physics: AlwaysScrollableScrollPhysics(),
                              shrinkWrap: true,
                              padding: EdgeInsets.symmetric(
                                vertical: paddingMedium,
                              ),
                              itemBuilder: (context, index) {
                                var inbox = controller.inboxes[index];
                                final isFromTrash =
                                    controller.selectedMenu.value?.title
                                        .toLowerCase() ==
                                    'sampah';
                                return ItemInbox(
                                  inbox: inbox,
                                  isDeletable: !isFromTrash,
                                  onArchive: () {
                                    controller.setSingleIds('${inbox.id}');
                                    controller.reqBulkArchiveInbox();
                                  },
                                  onDelete: () {
                                    controller.setSingleIds('${inbox.id}');
                                    if (isFromTrash) {
                                      _showDeleteDialog(context, () {
                                        controller.reqBulkDeleteInbox(
                                          isHardDelete: true,
                                        );
                                      });
                                    } else {
                                      controller.reqBulkDeleteInbox(
                                        isHardDelete: false,
                                      );
                                    }
                                  },
                                  onSelect: () {
                                    controller.inboxes[index].inboxSelected =
                                        !inbox.inboxSelected;
                                    controller.inboxes.refresh();
                                    controller.setSelectedIds();
                                  },
                                  icon: controller.iconInbox(inbox),
                                  onTap: () {
                                    controller.ids.clear();
                                    if (!(inbox.isRead ?? false)) {
                                      controller.setSingleIds('${inbox.id}');
                                      controller.reqBulkReadInbox();
                                    } else {
                                      controller.goToDetail(id: '${inbox.id}');
                                    }
                                  },
                                );
                              },

                              itemCount: controller.inboxes.length,
                            ),
                          ),
                        ),
                        if (controller.isLoading.value &&
                            controller.currentPage.value > 0)
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(),
                          ),
                      ],
                    );
                  }),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

void _showDeleteDialog(BuildContext context, VoidCallback onDelete) {
  PdlBaseDialog(
    context: context,
    child: PdlDialogContent(
      message: 'confirm_delete_permanent_message_str'.tr,
      onTap: () {
        Get.back();
        onDelete();
      },
    ),
  );
}

class _AppBar extends StatelessWidget {
  const _AppBar();

  @override
  Widget build(BuildContext context) {
    InboxListController controller = Get.find();
    return SizedBox(
      height: 56, // standard AppBar height
      child: Stack(
        alignment: Alignment.centerLeft,
        children: [
          InkWell(
            onTap: () => Navigator.of(context).pop(),
            child: Icon(Icons.arrow_back),
          ),
          Center(
            child: Text(
              "Inbox",
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
          ),
          Obx(
            () =>
                (controller.inboxes.firstWhereOrNull(
                          (element) => element.inboxSelected,
                        ) !=
                        null)
                    ? Align(
                      alignment: Alignment.centerRight,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          InkWell(
                            onTap: () {
                              controller.reqBulkArchiveInbox();
                            },
                            child: Utils.cachedSvgWrapper(
                              width: 25,
                              'icon/ic-linear-notes.svg',
                              color:
                                  Get.isDarkMode
                                      ? kColorTextDark
                                      : kColorTextLight,
                            ),
                          ),
                          SizedBox(width: 10),
                          InkWell(
                            onTap: () {
                              controller.reqBulkReadInbox();
                            },
                            child: Utils.cachedSvgWrapper(
                              'icon/ic-linear-letter -unread.svg',
                              width: 25,
                              color:
                                  Get.isDarkMode
                                      ? kColorTextDark
                                      : kColorTextLight,
                            ),
                          ),
                          SizedBox(width: 10),
                          InkWell(
                            onTap: () {
                              final isFromTrash =
                                  controller.selectedMenu.value?.title
                                      .toLowerCase() ==
                                  'sampah';
                              if (isFromTrash) {
                                _showDeleteDialog(context, () {
                                  controller.reqBulkDeleteInbox(
                                    isHardDelete: true,
                                  );
                                });
                              } else {
                                controller.reqBulkDeleteInbox(
                                  isHardDelete: false,
                                );
                              }
                            },
                            child: Utils.cachedSvgWrapper(
                              'icon/ic-linear-trash-bin.svg',
                              width: 25,
                              color:
                                  Get.isDarkMode
                                      ? kColorTextDark
                                      : kColorTextLight,
                            ),
                          ),
                        ],
                      ),
                    )
                    : SizedBox.shrink(),
          ),
        ],
      ),
    );
  }
}
