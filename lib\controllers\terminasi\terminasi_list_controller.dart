import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/termination_model.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pdl_superapp/utils/keys.dart';

class TerminasiListController extends BaseControllers {
  final TextEditingController reasonTextController = TextEditingController();
  final TextEditingController polisTextController = TextEditingController();
  final RxInt activeTabIndex = 0.obs;
  final RxString agentCode = ''.obs;
  late SharedPreferences prefs;

  RxList<TerminationModel> selfTerminasiList = <TerminationModel>[].obs;
  RxList<TerminationModel> teamTerminasiList = <TerminationModel>[].obs;

  @override
  void onInit() async {
    super.onInit();

    prefs = await SharedPreferences.getInstance();
    agentCode.value = prefs.getString(kStorageAgentCode) ?? '';
    await getTerminasiList();
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    switch (requestCode) {
      case kReqSubmitTermination:
        Get.offNamed(
          Routes.TERMINASI_DETAIL,
          arguments: {
            kArgsTerminationReason: reasonTextController.text,
            kArgsSelfTermination: true,
          },
        );
        break;
      case kReqGetTerminationList:
        selfTerminasiList.clear();
        teamTerminasiList.clear();
        var datas = response['content'];
        for (int i = 0; i < datas.length; i++) {
          final terminasiData = TerminationModel.fromJson(datas[i]);
          if ((terminasiData.target?.agentCode) == agentCode.value) {
            selfTerminasiList.add(terminasiData);
          } else {
            teamTerminasiList.add(terminasiData);
          }
        }
        break;
      default:
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
  }

  changeActiveTab(int active) {
    activeTabIndex(active);
  }

  getTerminasiList() async {
    await api.getTerminationList(
      controllers: this,
      params: null,
      code: kReqGetTerminationList,
    );
  }

  void refreshData() {}
}
